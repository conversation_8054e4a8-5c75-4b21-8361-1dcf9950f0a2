import React from 'react';
import { NavigationHeader } from './NavigationHeader';
import { HeroSection } from './HeroSection';
import { FeaturesSection } from './FeaturesSection';
import { BenefitsSection } from './BenefitsSection';
import { TestimonialsSection } from './TestimonialsSection';
import { CTASection } from './CTASection';
import { FooterSection } from './FooterSection';

export const LandingPage: React.FC = () => {
  return (
    <div className="min-h-screen bg-white">
      {/* Test message to verify the page loads */}
      <div className="fixed top-4 right-4 bg-green-500 text-white px-4 py-2 rounded-lg z-50">
        ✅ Landing Page Loaded Successfully!
      </div>
      
      {/* Fixed Navigation Header */}
      <NavigationHeader />
      
      {/* Main Content with proper spacing for fixed header */}
      <div className="pt-16">
        {/* Hero Section - Main value proposition */}
        <HeroSection />
        
        {/* Features Section - Key product features */}
        <FeaturesSection />
        
        {/* Benefits Section - Value propositions */}
        <BenefitsSection />
        
        {/* Testimonials Section - Social proof */}
        <TestimonialsSection />
        
        {/* Call-to-Action Section - Conversion */}
        <CTASection />
        
        {/* Footer Section - Links and info */}
        <FooterSection />
      </div>
    </div>
  );
};
